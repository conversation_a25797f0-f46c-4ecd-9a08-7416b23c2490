const { Pool } = require('pg');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
};

class TripDriverPopulationFixer {
  constructor() {
    this.pool = new Pool(dbConfig);
  }

  async executeComprehensiveFix() {
    console.log('🔧 COMPREHENSIVE TRIP DRIVER POPULATION FIX');
    console.log('='.repeat(60));
    
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      console.log('\n📋 Step 1: Enhanced Driver Capture Function');
      await this.createEnhancedDriverCaptureFunction(client);
      
      console.log('\n📋 Step 2: Retroactive Data Recovery');
      await this.performRetroactiveDataRecovery(client);
      
      console.log('\n📋 Step 3: Enhanced Database Trigger');
      await this.createEnhancedTrigger(client);
      
      console.log('\n📋 Step 4: Data Integrity Validation');
      await this.validateDataIntegrity(client);
      
      await client.query('COMMIT');
      console.log('\n✅ ALL FIXES APPLIED SUCCESSFULLY!');
      
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Fix failed, rolling back:', error.message);
      throw error;
    } finally {
      client.release();
      await this.pool.end();
    }
  }

  async createEnhancedDriverCaptureFunction(client) {
    console.log('   Creating enhanced capture_active_driver_for_trip function...');
    
    const enhancedFunctionSQL = `
      -- Enhanced function with fallback logic for shift transition gaps
      CREATE OR REPLACE FUNCTION capture_active_driver_for_trip(
        p_truck_id INTEGER,
        p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) RETURNS TABLE (
        driver_id INTEGER,
        driver_name VARCHAR(100),
        employee_id VARCHAR(20),
        shift_id INTEGER,
        shift_type shift_type
      ) AS $$
      BEGIN
        -- First try to get active driver (original logic)
        RETURN QUERY
        SELECT 
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = p_truck_id
          AND ds.status = 'active'
          AND (
            -- Single date shifts (backward compatibility)
            (ds.start_date IS NULL AND ds.shift_date = p_timestamp::date) OR
            -- Multi-day shifts (enhanced logic)
            (ds.start_date IS NOT NULL AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date)
          )
          AND p_timestamp::time BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
        
        -- ENHANCED: If no active driver found, use fallback logic for shift transition gaps
        IF NOT FOUND THEN
          RETURN QUERY
          SELECT 
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = p_truck_id
            AND ds.status IN ('completed', 'active')
            AND (
              -- For daytime trips (06:00-18:00), prefer day shifts
              (p_timestamp::time BETWEEN '06:00:00' AND '18:00:00' AND ds.shift_type = 'day') OR
              -- For nighttime trips (18:00-06:00), prefer night shifts  
              ((p_timestamp::time >= '18:00:00' OR p_timestamp::time <= '06:00:00') AND ds.shift_type = 'night')
            )
            AND (
              -- Multi-day shifts: check if timestamp falls within date range
              (ds.start_date IS NOT NULL AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date) OR
              -- Single-day shifts: check recent completed shifts
              (ds.start_date IS NULL AND ds.shift_date >= p_timestamp::date - interval '1 day')
            )
          ORDER BY 
            -- Prefer active shifts over completed
            CASE WHEN ds.status = 'active' THEN 0 ELSE 1 END,
            -- Prefer shifts closer to the timestamp
            CASE
              WHEN ds.end_date IS NOT NULL THEN ABS(EXTRACT(EPOCH FROM (p_timestamp::timestamp - ds.end_date::timestamp)))
              ELSE ABS(EXTRACT(EPOCH FROM (p_timestamp::timestamp - ds.shift_date::timestamp)))
            END ASC,
            ds.created_at DESC
          LIMIT 1;
        END IF;
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    await client.query(enhancedFunctionSQL);
    console.log('   ✅ Enhanced driver capture function created');
    
    // Test the enhanced function
    const testResult = await client.query(
      'SELECT * FROM capture_active_driver_for_trip($1, $2)',
      [1, '2025-07-12 10:32:09']  // Test with problematic trip timestamp
    );
    
    if (testResult.rows.length > 0) {
      const driver = testResult.rows[0];
      console.log(`   ✅ Test successful: Found driver ${driver.driver_id} (${driver.driver_name}) for truck 1`);
    } else {
      console.log('   ⚠️ Test warning: No driver found even with enhanced logic');
    }
  }

  async performRetroactiveDataRecovery(client) {
    console.log('   Performing retroactive data recovery for trips 7-10...');
    
    // Get trips that need recovery
    const missingTripsQuery = `
      SELECT 
        t.id,
        t.trip_number,
        t.loading_start_time,
        a.truck_id
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      WHERE t.performed_by_driver_id IS NULL
        AND t.trip_number BETWEEN 7 AND 10
      ORDER BY t.id;
    `;
    
    const missingTrips = await client.query(missingTripsQuery);
    console.log(`   Found ${missingTrips.rows.length} trips needing recovery`);
    
    let recoveredCount = 0;
    
    for (const trip of missingTrips.rows) {
      console.log(`   Processing trip ${trip.trip_number} (ID: ${trip.id})...`);
      
      // Use enhanced function to get driver data
      const driverResult = await client.query(
        'SELECT * FROM capture_active_driver_for_trip($1, $2)',
        [trip.truck_id, trip.loading_start_time]
      );
      
      if (driverResult.rows.length > 0) {
        const driver = driverResult.rows[0];
        
        // Update trip with recovered driver data
        const updateResult = await client.query(`
          UPDATE trip_logs 
          SET 
            performed_by_driver_id = $1,
            performed_by_driver_name = $2,
            performed_by_employee_id = $3,
            performed_by_shift_id = $4,
            performed_by_shift_type = $5,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $6
          RETURNING trip_number;
        `, [
          driver.driver_id,
          driver.driver_name,
          driver.employee_id,
          driver.shift_id,
          driver.shift_type,
          trip.id
        ]);
        
        if (updateResult.rows.length > 0) {
          console.log(`     ✅ Trip ${trip.trip_number}: Recovered driver ${driver.driver_name} (${driver.employee_id})`);
          recoveredCount++;
        }
      } else {
        console.log(`     ❌ Trip ${trip.trip_number}: No driver data available for recovery`);
      }
    }
    
    console.log(`   ✅ Recovery complete: ${recoveredCount}/${missingTrips.rows.length} trips recovered`);
  }

  async createEnhancedTrigger(client) {
    console.log('   Creating enhanced database trigger...');
    
    // Drop existing trigger and recreate with enhanced logic
    await client.query('DROP TRIGGER IF EXISTS trigger_auto_capture_trip_driver ON trip_logs;');
    
    const enhancedTriggerSQL = `
      CREATE OR REPLACE FUNCTION auto_capture_trip_driver()
      RETURNS TRIGGER AS $$
      DECLARE
          driver_info RECORD;
          truck_id INTEGER;
      BEGIN
          -- Get truck_id from assignment
          SELECT a.truck_id INTO truck_id
          FROM assignments a
          WHERE a.id = NEW.assignment_id;
          
          -- Only capture driver info if not already set and trip is starting
          IF NEW.performed_by_driver_id IS NULL AND NEW.loading_start_time IS NOT NULL THEN
              -- Use enhanced capture function with fallback logic
              SELECT * INTO driver_info
              FROM capture_active_driver_for_trip(truck_id, NEW.loading_start_time);
              
              IF FOUND THEN
                  NEW.performed_by_driver_id := driver_info.driver_id;
                  NEW.performed_by_driver_name := driver_info.driver_name;
                  NEW.performed_by_employee_id := driver_info.employee_id;
                  NEW.performed_by_shift_id := driver_info.shift_id;
                  NEW.performed_by_shift_type := driver_info.shift_type;
              END IF;
          END IF;
          
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
      
      -- Create enhanced trigger for both INSERT and UPDATE
      CREATE TRIGGER trigger_auto_capture_trip_driver
          BEFORE INSERT OR UPDATE ON trip_logs
          FOR EACH ROW
          EXECUTE FUNCTION auto_capture_trip_driver();
    `;
    
    await client.query(enhancedTriggerSQL);
    console.log('   ✅ Enhanced trigger created for INSERT and UPDATE operations');
  }

  async validateDataIntegrity(client) {
    console.log('   Validating data integrity after fixes...');
    
    const validationQuery = `
      SELECT 
        COUNT(*) as total_trips,
        COUNT(performed_by_driver_id) as trips_with_driver_id,
        COUNT(performed_by_driver_name) as trips_with_driver_name,
        COUNT(performed_by_employee_id) as trips_with_employee_id,
        COUNT(performed_by_shift_id) as trips_with_shift_id,
        COUNT(performed_by_shift_type) as trips_with_shift_type,
        ROUND(COUNT(performed_by_driver_id) * 100.0 / COUNT(*), 1) as integrity_percentage
      FROM trip_logs 
      WHERE trip_number BETWEEN 5 AND 10;
    `;
    
    const result = await client.query(validationQuery);
    const stats = result.rows[0];
    
    console.log('   📊 Data Integrity Report:');
    console.log(`      Total trips: ${stats.total_trips}`);
    console.log(`      Complete driver data: ${stats.trips_with_driver_id}/${stats.total_trips}`);
    console.log(`      Integrity score: ${stats.integrity_percentage}%`);
    
    if (stats.integrity_percentage === '100.0') {
      console.log('   ✅ Perfect data integrity achieved!');
    } else {
      console.log(`   ⚠️ Data integrity: ${stats.integrity_percentage}% - some trips still missing data`);
    }
    
    // Show detailed breakdown
    const detailQuery = `
      SELECT 
        id,
        trip_number,
        status,
        performed_by_driver_name,
        performed_by_employee_id,
        performed_by_shift_type,
        loading_start_time
      FROM trip_logs 
      WHERE trip_number BETWEEN 5 AND 10
      ORDER BY trip_number;
    `;
    
    const detailResult = await client.query(detailQuery);
    
    console.log('\n   📋 Trip Details After Fix:');
    console.log('   Trip | Status         | Driver Name    | Employee ID | Shift Type | Loading Start');
    console.log('   ' + '-'.repeat(85));
    
    detailResult.rows.forEach(trip => {
      const driverName = trip.performed_by_driver_name || 'NULL';
      const employeeId = trip.performed_by_employee_id || 'NULL';
      const shiftType = trip.performed_by_shift_type || 'NULL';
      const loadingStart = trip.loading_start_time ? trip.loading_start_time.toISOString().slice(0, 19) : 'NULL';
      
      console.log(
        `   ${String(trip.trip_number).padEnd(4)} | ${String(trip.status).padEnd(14)} | ${String(driverName).padEnd(14)} | ${String(employeeId).padEnd(11)} | ${String(shiftType).padEnd(10)} | ${loadingStart}`
      );
    });
  }
}

// Execute the comprehensive fix
async function main() {
  try {
    const fixer = new TripDriverPopulationFixer();
    await fixer.executeComprehensiveFix();
    
    console.log('\n🎉 MISSION ACCOMPLISHED!');
    console.log('   ✅ Enhanced driver capture function with fallback logic');
    console.log('   ✅ Retroactive data recovery for trips 7-10');
    console.log('   ✅ Enhanced database trigger for future protection');
    console.log('   ✅ Data integrity validation completed');
    console.log('\n📈 BUSINESS IMPACT:');
    console.log('   🎯 Trip Monitoring will show correct driver info during shift transitions');
    console.log('   📊 Truck Trip Summary will have accurate driver attribution');
    console.log('   🔒 Future trips will automatically capture driver data even during shift gaps');
    console.log('   📋 Historical data is now complete and immutable');
    
  } catch (error) {
    console.error('\n💥 CRITICAL ERROR:', error.message);
    console.error('   Please review the error and retry the fix');
    process.exit(1);
  }
}

main();
