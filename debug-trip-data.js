const { Pool } = require('pg');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
};

async function debugTripData() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔍 Analyzing trip_logs data pattern...\n');
    
    // Query problematic trips
    const tripsQuery = `
      SELECT 
        t.id, 
        t.trip_number, 
        t.status,
        a.truck_id,
        dt.truck_number,
        t.performed_by_driver_id, 
        t.performed_by_driver_name, 
        t.performed_by_employee_id,
        t.performed_by_shift_id,
        t.performed_by_shift_type,
        t.loading_start_time,
        t.created_at
      FROM trip_logs t 
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE t.trip_number BETWEEN 5 AND 10 
      ORDER BY t.id;
    `;
    
    const tripsResult = await pool.query(tripsQuery);
    
    console.log('📊 Trip Data Analysis:');
    console.log('='.repeat(120));
    console.log('ID  | Trip# | Status         | Truck | Driver ID | Driver Name    | Employee ID | Shift ID | Shift Type | Loading Start');
    console.log('='.repeat(120));
    
    tripsResult.rows.forEach(trip => {
      console.log(
        `${String(trip.id).padEnd(3)} | ${String(trip.trip_number).padEnd(5)} | ${String(trip.status).padEnd(14)} | ${String(trip.truck_number).padEnd(5)} | ${String(trip.performed_by_driver_id || 'NULL').padEnd(9)} | ${String(trip.performed_by_driver_name || 'NULL').padEnd(14)} | ${String(trip.performed_by_employee_id || 'NULL').padEnd(11)} | ${String(trip.performed_by_shift_id || 'NULL').padEnd(8)} | ${String(trip.performed_by_shift_type || 'NULL').padEnd(10)} | ${trip.loading_start_time?.toISOString().slice(0, 19) || 'NULL'}`
      );
    });
    
    console.log('\n🔍 Active Driver Shifts Analysis:');
    console.log('='.repeat(80));
    
    const shiftsQuery = `
      SELECT 
        ds.id,
        ds.truck_id,
        dt.truck_number,
        ds.driver_id,
        d.full_name,
        d.employee_id,
        ds.shift_type,
        ds.status,
        ds.start_date,
        ds.end_date,
        ds.start_time,
        ds.end_time
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.status = 'active'
      ORDER BY ds.truck_id;
    `;
    
    const shiftsResult = await pool.query(shiftsQuery);
    
    console.log('Shift ID | Truck | Driver ID | Driver Name    | Employee ID | Shift Type | Status | Date Range');
    console.log('='.repeat(80));
    
    shiftsResult.rows.forEach(shift => {
      const dateRange = shift.start_date && shift.end_date 
        ? `${shift.start_date.toISOString().slice(0, 10)} to ${shift.end_date.toISOString().slice(0, 10)}`
        : 'NULL';
      console.log(
        `${String(shift.id).padEnd(8)} | ${String(shift.truck_number).padEnd(5)} | ${String(shift.driver_id).padEnd(9)} | ${String(shift.full_name).padEnd(14)} | ${String(shift.employee_id).padEnd(11)} | ${String(shift.shift_type).padEnd(10)} | ${String(shift.status).padEnd(6)} | ${dateRange}`
      );
    });
    
    console.log('\n🔍 Historical Shift Analysis:');
    console.log('='.repeat(80));

    // Check all shifts for the trucks with problems
    const allShiftsQuery = `
      SELECT
        ds.id,
        ds.truck_id,
        dt.truck_number,
        ds.driver_id,
        d.full_name,
        ds.shift_type,
        ds.status,
        ds.start_date,
        ds.end_date,
        ds.start_time,
        ds.end_time,
        ds.created_at
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      JOIN dump_trucks dt ON ds.truck_id = dt.id
      WHERE ds.truck_id IN (1, 3)  -- Problem trucks
      ORDER BY ds.truck_id, ds.created_at DESC;
    `;

    const allShiftsResult = await pool.query(allShiftsQuery);

    console.log('Shift ID | Truck | Driver | Shift Type | Status     | Date Range | Time Range | Created');
    console.log('='.repeat(80));

    allShiftsResult.rows.forEach(shift => {
      const dateRange = shift.start_date && shift.end_date
        ? `${shift.start_date.toISOString().slice(0, 10)} to ${shift.end_date.toISOString().slice(0, 10)}`
        : 'NULL';
      const timeRange = `${shift.start_time} - ${shift.end_time}`;
      console.log(
        `${String(shift.id).padEnd(8)} | ${String(shift.truck_number).padEnd(5)} | ${String(shift.full_name).padEnd(6)} | ${String(shift.shift_type).padEnd(10)} | ${String(shift.status).padEnd(10)} | ${dateRange.padEnd(10)} | ${timeRange.padEnd(10)} | ${shift.created_at.toISOString().slice(0, 19)}`
      );
    });

    console.log('\n🧪 Testing capture_active_driver_for_trip function:');
    console.log('='.repeat(60));

    // Test the function for each truck that has problematic trips
    const problemTrucks = [...new Set(tripsResult.rows.filter(t => !t.performed_by_driver_id).map(t => t.truck_id))];

    for (const truckId of problemTrucks) {
      console.log(`\n🚛 Testing truck ID ${truckId}:`);

      const functionTestQuery = `SELECT * FROM capture_active_driver_for_trip($1, $2)`;

      // Test at the actual time of the problematic trips
      const problemTripsForTruck = tripsResult.rows.filter(t => t.truck_id === truckId && !t.performed_by_driver_id);

      for (const trip of problemTripsForTruck) {
        const testTimestamp = trip.loading_start_time;
        console.log(`   Testing trip ${trip.trip_number} at ${testTimestamp.toISOString()}:`);

        try {
          const functionResult = await pool.query(functionTestQuery, [truckId, testTimestamp]);

          if (functionResult.rows.length > 0) {
            const driver = functionResult.rows[0];
            console.log(`     ✅ Function returned: Driver ${driver.driver_id} (${driver.driver_name}) - ${driver.shift_type} shift`);
          } else {
            console.log(`     ❌ Function returned no driver for truck ${truckId} at ${testTimestamp.toISOString()}`);
          }
        } catch (error) {
          console.log(`     💥 Function error: ${error.message}`);
        }
      }
    }
    
    console.log('\n📋 Summary:');
    console.log('='.repeat(40));
    const workingTrips = tripsResult.rows.filter(t => t.performed_by_driver_id);
    const brokenTrips = tripsResult.rows.filter(t => !t.performed_by_driver_id);
    
    console.log(`✅ Working trips (with driver data): ${workingTrips.length}`);
    console.log(`❌ Broken trips (missing driver data): ${brokenTrips.length}`);
    console.log(`🔧 Active driver shifts available: ${shiftsResult.rows.length}`);
    
    if (brokenTrips.length > 0) {
      console.log('\n🚨 ISSUE IDENTIFIED:');
      console.log(`   - Trips ${brokenTrips.map(t => t.trip_number).join(', ')} are missing performed_by_* data`);
      console.log(`   - These trips were created between ${brokenTrips[0].created_at.toISOString()} and ${brokenTrips[brokenTrips.length-1].created_at.toISOString()}`);
      console.log(`   - Active driver shifts exist but were not captured during trip creation`);
    }
    
  } catch (error) {
    console.error('❌ Database query failed:', error.message);
  } finally {
    await pool.end();
  }
}

debugTripData();
