const { Pool } = require('pg');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'hauling_qr_system',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'PostgreSQLPassword',
};

class TripDriverPopulationTester {
  constructor() {
    this.pool = new Pool(dbConfig);
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 COMPREHENSIVE TRIP DRIVER POPULATION TEST SUITE');
    console.log('='.repeat(60));
    
    try {
      await this.testCurrentDataIntegrity();
      await this.testRetroactiveDataRecovery();
      await this.testEnhancedDriverCapture();
      await this.testShiftTransitionHandling();
      await this.testDatabaseConstraints();
      
      this.printTestSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    } finally {
      await this.pool.end();
    }
  }

  async testCurrentDataIntegrity() {
    console.log('\n📊 TEST 1: Current Data Integrity Analysis');
    console.log('-'.repeat(50));
    
    try {
      // Check current state of trip_logs performed_by_* fields
      const integrityQuery = `
        SELECT 
          COUNT(*) as total_trips,
          COUNT(performed_by_driver_id) as trips_with_driver_id,
          COUNT(performed_by_driver_name) as trips_with_driver_name,
          COUNT(performed_by_employee_id) as trips_with_employee_id,
          COUNT(performed_by_shift_id) as trips_with_shift_id,
          COUNT(performed_by_shift_type) as trips_with_shift_type
        FROM trip_logs 
        WHERE trip_number BETWEEN 5 AND 10;
      `;
      
      const result = await this.pool.query(integrityQuery);
      const stats = result.rows[0];
      
      console.log(`Total trips analyzed: ${stats.total_trips}`);
      console.log(`Trips with driver_id: ${stats.trips_with_driver_id}/${stats.total_trips}`);
      console.log(`Trips with driver_name: ${stats.trips_with_driver_name}/${stats.total_trips}`);
      console.log(`Trips with employee_id: ${stats.trips_with_employee_id}/${stats.total_trips}`);
      console.log(`Trips with shift_id: ${stats.trips_with_shift_id}/${stats.total_trips}`);
      console.log(`Trips with shift_type: ${stats.trips_with_shift_type}/${stats.total_trips}`);
      
      const integrityScore = (
        parseInt(stats.trips_with_driver_id) + 
        parseInt(stats.trips_with_driver_name) + 
        parseInt(stats.trips_with_employee_id) + 
        parseInt(stats.trips_with_shift_id) + 
        parseInt(stats.trips_with_shift_type)
      ) / (parseInt(stats.total_trips) * 5) * 100;
      
      console.log(`Data integrity score: ${integrityScore.toFixed(1)}%`);
      
      this.testResults.push({
        test: 'Data Integrity',
        status: integrityScore === 100 ? 'PASS' : 'FAIL',
        score: integrityScore,
        details: `${stats.trips_with_driver_id}/${stats.total_trips} trips have complete driver data`
      });
      
    } catch (error) {
      console.error('❌ Data integrity test failed:', error.message);
      this.testResults.push({
        test: 'Data Integrity',
        status: 'ERROR',
        details: error.message
      });
    }
  }

  async testRetroactiveDataRecovery() {
    console.log('\n🔄 TEST 2: Retroactive Data Recovery Logic');
    console.log('-'.repeat(50));
    
    try {
      // Test logic to recover missing driver data for trips 7-10
      const recoveryQuery = `
        WITH trip_context AS (
          SELECT 
            t.id,
            t.trip_number,
            t.loading_start_time,
            a.truck_id,
            dt.truck_number
          FROM trip_logs t
          JOIN assignments a ON t.assignment_id = a.id
          JOIN dump_trucks dt ON a.truck_id = dt.id
          WHERE t.performed_by_driver_id IS NULL
            AND t.trip_number BETWEEN 7 AND 10
        ),
        historical_shifts AS (
          SELECT DISTINCT ON (tc.truck_id)
            tc.id as trip_id,
            tc.trip_number,
            tc.loading_start_time,
            tc.truck_id,
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type,
            ds.status,
            ds.end_date,
            ABS(EXTRACT(EPOCH FROM (tc.loading_start_time - COALESCE(ds.end_date, ds.shift_date)::timestamp))) as time_diff_seconds
          FROM trip_context tc
          JOIN driver_shifts ds ON ds.truck_id = tc.truck_id
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.status IN ('completed', 'active')
            AND ds.shift_type = 'day'  -- Focus on day shifts for daytime trips
          ORDER BY tc.truck_id, time_diff_seconds ASC
        )
        SELECT 
          trip_id,
          trip_number,
          loading_start_time,
          truck_id,
          driver_id,
          driver_name,
          employee_id,
          shift_id,
          shift_type,
          status as shift_status,
          ROUND(time_diff_seconds / 3600.0, 1) as hours_since_shift_end
        FROM historical_shifts
        ORDER BY trip_number;
      `;
      
      const recoveryResult = await this.pool.query(recoveryQuery);
      
      console.log('Recoverable driver data for missing trips:');
      console.log('Trip | Driver ID | Driver Name    | Employee ID | Shift ID | Hours Since Shift End');
      console.log('-'.repeat(80));
      
      recoveryResult.rows.forEach(row => {
        console.log(
          `${String(row.trip_number).padEnd(4)} | ${String(row.driver_id).padEnd(9)} | ${String(row.driver_name).padEnd(14)} | ${String(row.employee_id).padEnd(11)} | ${String(row.shift_id).padEnd(8)} | ${row.hours_since_shift_end}`
        );
      });
      
      const recoverableTrips = recoveryResult.rows.length;
      const expectedTrips = 4; // Trips 7-10
      
      this.testResults.push({
        test: 'Retroactive Recovery',
        status: recoverableTrips === expectedTrips ? 'PASS' : 'PARTIAL',
        details: `${recoverableTrips}/${expectedTrips} trips can be recovered with historical data`
      });
      
    } catch (error) {
      console.error('❌ Retroactive recovery test failed:', error.message);
      this.testResults.push({
        test: 'Retroactive Recovery',
        status: 'ERROR',
        details: error.message
      });
    }
  }

  async testEnhancedDriverCapture() {
    console.log('\n🎯 TEST 3: Enhanced Driver Capture Function');
    console.log('-'.repeat(50));
    
    try {
      // Test enhanced capture logic that includes fallback to recent completed shifts
      const enhancedCaptureQuery = `
        CREATE OR REPLACE FUNCTION capture_active_driver_for_trip_enhanced_test(
          p_truck_id INTEGER,
          p_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) RETURNS TABLE (
          driver_id INTEGER,
          driver_name VARCHAR(100),
          employee_id VARCHAR(20),
          shift_id INTEGER,
          shift_type shift_type,
          source VARCHAR(20)
        ) AS $$
        BEGIN
          -- First try to get active driver
          RETURN QUERY
          SELECT 
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type,
            'active'::VARCHAR(20) as source
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = p_truck_id
            AND ds.status = 'active'
            AND (
              (ds.start_date IS NULL AND ds.shift_date = p_timestamp::date) OR
              (ds.start_date IS NOT NULL AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date)
            )
            AND p_timestamp::time BETWEEN ds.start_time AND 
                CASE 
                  WHEN ds.end_time < ds.start_time 
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time 
                END
          ORDER BY ds.created_at DESC
          LIMIT 1;
          
          -- If no active driver found, get most recent completed shift
          IF NOT FOUND THEN
            RETURN QUERY
            SELECT 
              ds.driver_id,
              d.full_name as driver_name,
              d.employee_id,
              ds.id as shift_id,
              ds.shift_type,
              'fallback'::VARCHAR(20) as source
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = p_truck_id
              AND ds.status = 'completed'
              AND (
                (ds.shift_type = 'day' AND p_timestamp::time BETWEEN '06:00:00' AND '18:00:00') OR
                (ds.shift_type = 'night' AND (p_timestamp::time >= '18:00:00' OR p_timestamp::time <= '06:00:00'))
              )
            ORDER BY 
              CASE 
                WHEN ds.end_date IS NOT NULL THEN ds.end_date
                ELSE ds.shift_date
              END DESC,
              ds.created_at DESC
            LIMIT 1;
          END IF;
        END;
        $$ LANGUAGE plpgsql;
      `;
      
      await this.pool.query(enhancedCaptureQuery);
      
      // Test the enhanced function with problematic trip timestamps
      const testTimestamps = [
        { trip: 7, timestamp: '2025-07-12 10:32:04', truck_id: 3 },
        { trip: 8, timestamp: '2025-07-12 10:32:09', truck_id: 1 },
        { trip: 9, timestamp: '2025-07-12 11:40:12', truck_id: 1 },
        { trip: 10, timestamp: '2025-07-12 11:40:56', truck_id: 1 }
      ];
      
      console.log('Enhanced function test results:');
      console.log('Trip | Truck | Driver Found | Source   | Driver Name');
      console.log('-'.repeat(55));
      
      let successCount = 0;
      
      for (const test of testTimestamps) {
        const testResult = await this.pool.query(
          'SELECT * FROM capture_active_driver_for_trip_enhanced_test($1, $2)',
          [test.truck_id, test.timestamp]
        );
        
        if (testResult.rows.length > 0) {
          const driver = testResult.rows[0];
          console.log(
            `${String(test.trip).padEnd(4)} | ${String(test.truck_id).padEnd(5)} | Yes          | ${String(driver.source).padEnd(8)} | ${driver.driver_name}`
          );
          successCount++;
        } else {
          console.log(
            `${String(test.trip).padEnd(4)} | ${String(test.truck_id).padEnd(5)} | No           | N/A      | N/A`
          );
        }
      }
      
      this.testResults.push({
        test: 'Enhanced Driver Capture',
        status: successCount === testTimestamps.length ? 'PASS' : 'PARTIAL',
        details: `${successCount}/${testTimestamps.length} problematic trips can be resolved with enhanced logic`
      });
      
    } catch (error) {
      console.error('❌ Enhanced driver capture test failed:', error.message);
      this.testResults.push({
        test: 'Enhanced Driver Capture',
        status: 'ERROR',
        details: error.message
      });
    }
  }

  async testShiftTransitionHandling() {
    console.log('\n🔄 TEST 4: Shift Transition Handling');
    console.log('-'.repeat(50));
    
    try {
      // Test shift transition scenarios
      const transitionQuery = `
        SELECT 
          ds.truck_id,
          dt.truck_number,
          ds.shift_type,
          ds.status,
          ds.start_date,
          ds.end_date,
          ds.start_time,
          ds.end_time,
          CASE 
            WHEN ds.status = 'active' AND ds.shift_type = 'day' 
              AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time
            THEN 'Day shift active'
            WHEN ds.status = 'active' AND ds.shift_type = 'night' 
              AND (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time)
            THEN 'Night shift active'
            WHEN ds.status = 'completed'
            THEN 'Shift completed'
            ELSE 'Shift inactive'
          END as current_status
        FROM driver_shifts ds
        JOIN dump_trucks dt ON ds.truck_id = dt.id
        WHERE ds.truck_id IN (1, 3)
        ORDER BY ds.truck_id, ds.shift_type, ds.created_at DESC;
      `;
      
      const transitionResult = await this.pool.query(transitionQuery);
      
      console.log('Current shift status for problem trucks:');
      console.log('Truck | Shift Type | Status    | Current Status');
      console.log('-'.repeat(50));
      
      transitionResult.rows.forEach(row => {
        console.log(
          `${String(row.truck_number).padEnd(5)} | ${String(row.shift_type).padEnd(10)} | ${String(row.status).padEnd(9)} | ${row.current_status}`
        );
      });
      
      // Check for coverage gaps
      const gapAnalysisQuery = `
        SELECT 
          truck_id,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_shifts,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_shifts,
          COUNT(CASE WHEN shift_type = 'day' THEN 1 END) as day_shifts,
          COUNT(CASE WHEN shift_type = 'night' THEN 1 END) as night_shifts
        FROM driver_shifts 
        WHERE truck_id IN (1, 3)
        GROUP BY truck_id;
      `;
      
      const gapResult = await this.pool.query(gapAnalysisQuery);
      
      console.log('\nShift coverage analysis:');
      console.log('Truck ID | Active | Completed | Day | Night | Coverage Status');
      console.log('-'.repeat(60));
      
      let coverageIssues = 0;
      
      gapResult.rows.forEach(row => {
        const hasActiveDayShift = row.active_shifts > 0 && row.day_shifts > 0;
        const hasActiveNightShift = row.active_shifts > 0 && row.night_shifts > 0;
        const coverageStatus = hasActiveDayShift && hasActiveNightShift ? 'Full Coverage' : 'Gap Detected';
        
        if (coverageStatus === 'Gap Detected') coverageIssues++;
        
        console.log(
          `${String(row.truck_id).padEnd(8)} | ${String(row.active_shifts).padEnd(6)} | ${String(row.completed_shifts).padEnd(9)} | ${String(row.day_shifts).padEnd(3)} | ${String(row.night_shifts).padEnd(5)} | ${coverageStatus}`
        );
      });
      
      this.testResults.push({
        test: 'Shift Transition Handling',
        status: coverageIssues === 0 ? 'PASS' : 'FAIL',
        details: `${coverageIssues} trucks have shift coverage gaps`
      });
      
    } catch (error) {
      console.error('❌ Shift transition test failed:', error.message);
      this.testResults.push({
        test: 'Shift Transition Handling',
        status: 'ERROR',
        details: error.message
      });
    }
  }

  async testDatabaseConstraints() {
    console.log('\n🔒 TEST 5: Database Constraints Validation');
    console.log('-'.repeat(50));
    
    try {
      // Check if trigger exists and is working
      const triggerQuery = `
        SELECT 
          trigger_name,
          event_manipulation,
          action_timing,
          action_statement
        FROM information_schema.triggers 
        WHERE trigger_name = 'trigger_auto_capture_trip_driver'
          AND event_object_table = 'trip_logs';
      `;
      
      const triggerResult = await this.pool.query(triggerQuery);
      
      if (triggerResult.rows.length > 0) {
        console.log('✅ Auto-capture trigger exists and is configured');
        console.log(`   Timing: ${triggerResult.rows[0].action_timing}`);
        console.log(`   Events: ${triggerResult.rows[0].event_manipulation}`);
      } else {
        console.log('❌ Auto-capture trigger is missing');
      }
      
      // Test constraint validation
      const constraintQuery = `
        SELECT 
          column_name,
          is_nullable,
          data_type
        FROM information_schema.columns 
        WHERE table_name = 'trip_logs' 
          AND column_name LIKE 'performed_by_%'
        ORDER BY column_name;
      `;
      
      const constraintResult = await this.pool.query(constraintQuery);
      
      console.log('\nPerformed_by_* column constraints:');
      console.log('Column Name | Nullable | Data Type');
      console.log('-'.repeat(40));
      
      constraintResult.rows.forEach(row => {
        console.log(
          `${String(row.column_name).padEnd(11)} | ${String(row.is_nullable).padEnd(8)} | ${row.data_type}`
        );
      });
      
      this.testResults.push({
        test: 'Database Constraints',
        status: triggerResult.rows.length > 0 ? 'PASS' : 'FAIL',
        details: `Trigger exists: ${triggerResult.rows.length > 0}, Columns: ${constraintResult.rows.length}`
      });
      
    } catch (error) {
      console.error('❌ Database constraints test failed:', error.message);
      this.testResults.push({
        test: 'Database Constraints',
        status: 'ERROR',
        details: error.message
      });
    }
  }

  printTestSummary() {
    console.log('\n📋 TEST SUITE SUMMARY');
    console.log('='.repeat(60));
    
    let passCount = 0;
    let failCount = 0;
    let errorCount = 0;
    
    this.testResults.forEach(result => {
      const statusIcon = result.status === 'PASS' ? '✅' : 
                        result.status === 'FAIL' ? '❌' : 
                        result.status === 'PARTIAL' ? '⚠️' : '💥';
      
      console.log(`${statusIcon} ${result.test}: ${result.status}`);
      console.log(`   ${result.details}`);
      
      if (result.status === 'PASS') passCount++;
      else if (result.status === 'FAIL' || result.status === 'PARTIAL') failCount++;
      else errorCount++;
    });
    
    console.log('\n📊 OVERALL RESULTS:');
    console.log(`   ✅ Passed: ${passCount}`);
    console.log(`   ❌ Failed: ${failCount}`);
    console.log(`   💥 Errors: ${errorCount}`);
    console.log(`   📈 Success Rate: ${(passCount / this.testResults.length * 100).toFixed(1)}%`);
    
    if (failCount === 0 && errorCount === 0) {
      console.log('\n🎉 ALL TESTS PASSED - Ready for implementation!');
    } else {
      console.log('\n🔧 ISSUES DETECTED - Implementation needed to fix failing tests');
    }
  }
}

// Run the test suite
const tester = new TripDriverPopulationTester();
tester.runAllTests();
